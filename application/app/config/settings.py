
import os
from dotenv import load_dotenv
load_dotenv()

class OMSConfigs:
    def __init__(self):

        # Database settings
        self.DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/rozana_oms")
        self.DATABASE_READ_URL = os.getenv("DATABASE_READ_URL", self.DATABASE_URL)

        # Redis settings
        self.REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
        self.REDIS_CACHE_DB = int(os.getenv("REDIS_CACHE_DB", "3"))

        # Environment settings
        self.APPLICATION_ENVIRONMENT = os.getenv("APPLICATION_ENVIRONMENT", "UAT")
        self.APP_NAME = os.getenv('APP_NAME', 'rozana-oms')
        self.APP_VERSION = os.getenv('APP_VERSION', '4.0.0')

        # Typesence settings
        self.TYPESENSE_HOST = os.getenv("TYPESENSE_HOST", "localhost")
        self.TYPESENSE_PORT = os.getenv("TYPESENSE_PORT", "8108")
        self.TYPESENSE_PROTOCOL = os.getenv("TYPESENSE_PROTOCOL", "http")
        self.TYPESENSE_API_KEY = os.getenv("TYPESENSE_API_KEY", "")
        self.TYPESENSE_COLLECTION_NAME = os.getenv("TYPESENSE_COLLECTION_NAME", "products")
        self.TYPESENSE_FREEBIES_COLLECTION_NAME = os.getenv("TYPESENSE_FREEBIES_COLLECTION_NAME", "freebies_products")

        # Razorpay settings
        self.RAZORPAY_INTEGRATION_ENABLED = os.getenv("RAZORPAY_INTEGRATION_ENABLED", "false").lower() == "true"
        self.RAZORPAY_KEY_ID = os.getenv("RAZORPAY_KEY_ID", "")
        self.RAZORPAY_KEY_SECRET = os.getenv("RAZORPAY_KEY_SECRET", "")
        self.RAZORPAY_WEBHOOK_SECRET = os.getenv("RAZORPAY_WEBHOOK_SECRET", "")
        self.RAZORPAY_BASE_URL = os.getenv("RAZORPAY_BASE_URL", "https://api.razorpay.com/v1")
        self.RAZORPAY_CURRENCY = os.getenv("RAZORPAY_CURRENCY", "INR")
        self.RAZORPAY_TIMEOUT = int(os.getenv("RAZORPAY_TIMEOUT", "30"))

        # Wallet settings
        self.WALLET_INTEGRATION_ENABLED = os.getenv("WALLET_INTEGRATION_ENABLED", "false").lower() == "true"
        self.WALLET_BASE_URL = os.getenv("WALLET_BASE_URL", "")
        self.WALLET_INTERNAL_API_KEY = os.getenv("WALLET_INTERNAL_API_KEY", "")

        # Sentry settings
        self.SENTRY_ENABLED = os.getenv("SENTRY_ENABLED", "false").lower() == "true"
        self.SENTRY_DSN = os.getenv("SENTRY_DSN", "")
        self.ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
        self.SENTRY_RELEASE = os.getenv("SENTRY_RELEASE", "rozana-oms-service@1.0.0")
        self.SENTRY_TRACES_SAMPLE_RATE = os.getenv("SENTRY_TRACES_SAMPLE_RATE", "0.1")
        self.SENTRY_PROFILES_SAMPLE_RATE = os.getenv("SENTRY_PROFILES_SAMPLE_RATE", "0.1")

        # Potions settings
        self.POTIONS_INTEGRATION_ENABLED = os.getenv("POTIONS_INTEGRATION_ENABLED", "true").lower() == "true"
        self.POTIONS_CLIENT_ID = os.getenv("POTIONS_CLIENT_ID", "")
        self.POTIONS_CLIENT_SECRET = os.getenv("POTIONS_CLIENT_SECRET", "")
        self.POTIONS_BASE_URL = os.getenv("POTIONS_BASE_URL", "")
        self.POTIONS_TIMEOUT = int(os.getenv("POTIONS_TIMEOUT", "60"))

        # Auth settings
        self.TOKEN_VALIDATION_URL = os.getenv("TOKEN_VALIDATION_URL", "")

        # Encryption settings
        self.ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY", "MOoRZhMT3c5yrE1A")

        # Product settings
        self.STOCK_CHECK_ENABLED = os.getenv("STOCK_CHECK_ENABLED", "false").lower() == "true"
        self.PRICE_CHECK_ENABLED = os.getenv("PRICE_CHECK_ENABLED", "true").lower() == "true"

        # Logging Core settings
        self.ASYNC_LOGGING = os.getenv("ASYNC_LOGGING", "false").lower() == "true"
        self.BATCH_PROCESSING = os.getenv("BATCH_PROCESSING", "false").lower() == "true"
        self.FIREHOSE_ENABLED = os.getenv("FIREHOSE_ENABLED", "false").lower() == "true"
        self.AUDIT_LOGGING_ENABLED = os.getenv("AUDIT_LOGGING_ENABLED", "false").lower() == "true"
        self.CAPTURE_RESPONSE_BODY = os.getenv("CAPTURE_RESPONSE_BODY", "false").lower() == "true"
        self.LOG_DEBUG_PRINTS = os.getenv("LOG_DEBUG_PRINTS", "false").lower() == "true"
        
        # Logging Stream Names
        self.APP_LOGS_STREAM_NAME = os.getenv("APP_LOGS_STREAM_NAME", "")
        self.AUDIT_LOGS_STREAM_NAME = os.getenv("AUDIT_LOGS_STREAM_NAME", "")
        self.AUDIT_LOGS_GET_STREAM_NAME = os.getenv("AUDIT_LOGS_GET_STREAM_NAME", "")
        self.LOG_BUFFER_TIMEOUT = int(os.getenv("LOG_BUFFER_TIMEOUT", "600"))

        # Logging Buffer Sizes
        self.APP_LOGS_CAPACITY = int(os.getenv("APP_LOGS_CAPACITY", "50"))
        self.AUDIT_LOGS_CAPACITY = int(os.getenv("AUDIT_LOGS_CAPACITY", "50"))
        self.AUDIT_LOGS_GET_CAPACITY = int(os.getenv("AUDIT_LOGS_GET_CAPACITY", "50"))
        self.LOG_PROCESSOR_POOL_SIZE = int(os.getenv("LOG_PROCESSOR_POOL_SIZE", "2"))
        self.MAX_QUEUE_SIZE = int(os.getenv("MAX_QUEUE_SIZE", "10"))

        # Firehose settings
        self.FIREHOSE_REGION_NAME = os.getenv("FIREHOSE_REGION_NAME", "ap-south-1")
        self.FIREHOSE_ACCESS_KEY_ID = os.getenv("FIREHOSE_ACCESS_KEY_ID", "")
        self.FIREHOSE_SECRET_ACCESS_KEY = os.getenv("FIREHOSE_SECRET_ACCESS_KEY", "")
        self.FIREHOSE_RETRY_COUNT = int(os.getenv("FIREHOSE_RETRY_COUNT", "3"))
        self.FIREHOSE_RETRY_DELAY = int(os.getenv("FIREHOSE_RETRY_DELAY", "1"))
