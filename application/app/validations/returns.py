from typing import List, Dict, Tuple
from sqlalchemy import text
from app.core.constants import OrderStatus

class ReturnsValidator:
    # Returns are eligible only after delivery
    allowed_order_statuses = [
        OrderStatus.TMS_DELIVERED,
    ]

    @staticmethod
    def validate_order_exists_and_status(conn, order_id: str) -> Tuple[object, str]:
        check_order_sql = """
            SELECT id, order_id, status, facility_name
            FROM orders
            WHERE order_id = :order_id
        """
        result = conn.execute(text(check_order_sql), {'order_id': order_id})
        order_row = result.fetchone()
        if not order_row:
            raise ValueError(f"Order {order_id} not found")
        if order_row.status not in ReturnsValidator.allowed_order_statuses:
            current_name = OrderStatus.get_customer_status_name(order_row.status)
            allowed_names = [OrderStatus.get_customer_status_name(s) for s in ReturnsValidator.allowed_order_statuses]
            raise ValueError(
                f"Order {order_id} cannot be returned. "
                f"Current status: {current_name} is not allowed. "
                f"Allowed status(es): {', '.join(allowed_names)}"
            )
        return order_row, order_row.facility_name

    @staticmethod
    def validate_items_exist_and_quantities(current_items: Dict[str, object], items_to_return: List[Dict]) -> List[object]:
        """Validate requested items exist and quantities do not exceed ordered quantities.

        Returns a list of matched item rows (from current_items) corresponding to items_to_return
        that passed existence and quantity checks. Aggregates and raises all errors at once.
        """
        errors: List[str] = []
        matched_rows: List[object] = []
        for item in items_to_return:
            sku = item['sku']
            qty = item['quantity']
            if sku not in current_items:
                errors.append(f"SKU {sku} not found in order")
                continue
            current_row = current_items[sku]
            # Compute effective available quantity for returns: prefer delivered, then fulfilled, then ordered quantity
            delivered_q = getattr(current_row, 'delivered_quantity', None)
            fulfilled_q = getattr(current_row, 'fulfilled_quantity', None)
            ordered_q = getattr(current_row, 'quantity', 0)
            # Safely convert to float while handling None
            try:
                delivered_val = float(delivered_q) if delivered_q is not None else 0.0
            except Exception:
                delivered_val = 0.0
            try:
                fulfilled_val = float(fulfilled_q) if fulfilled_q is not None else 0.0
            except Exception:
                fulfilled_val = 0.0
            try:
                ordered_val = float(ordered_q) if ordered_q is not None else 0.0
            except Exception:
                ordered_val = 0.0
            effective_available = delivered_val if delivered_val > 0 else (fulfilled_val if fulfilled_val > 0 else ordered_val)

            if float(qty) > effective_available:
                errors.append(
                    f"Cannot return {qty} units of {sku}. Order only contains {effective_available} units"
                )
                continue
            matched_rows.append(current_row)
        if errors:
            raise ValueError(f"Item validation failed: {'; '.join(errors)}")
        return matched_rows

    @staticmethod
    def validate_item_eligibility(item_row, sku: str) -> List[str]:
        errors = []
        if not item_row.is_returnable:
            errors.append(f"SKU {sku} is not returnable (is_returnable=False)")
        if item_row.return_type not in ['10', '11']:
            errors.append(f"SKU {sku} return type '{item_row.return_type}' does not allow returns (must be '10' or '11')")
        # Items eligible for return only after delivery
        allowed_item_statuses = [OrderStatus.TMS_DELIVERED]
        if item_row.status not in allowed_item_statuses:
            current_name = OrderStatus.get_customer_status_name(item_row.status)
            allowed_names = [OrderStatus.get_customer_status_name(s) for s in allowed_item_statuses]
            errors.append(
                f"SKU {sku} status {current_name} does not allow returns "
                f"(must be in {', '.join(allowed_names)})"
            )
        return errors

    @staticmethod
    def validate_full_return_eligibility(all_items: List[object]) -> None:
        errors: List[str] = []
        for row in all_items:
            errors.extend(ReturnsValidator.validate_item_eligibility(row, row.sku))
        if errors:
            raise ValueError(f"Full order return not allowed: {'; '.join(errors)}")

    @staticmethod
    def validate_items_eligibility(matched_rows: List[object]) -> None:
        """Validate eligibility for a set of matched item rows (aggregates all errors)."""
        errors: List[str] = []
        for row in matched_rows:
            errors.extend(ReturnsValidator.validate_item_eligibility(row, row.sku))
        if errors:
            raise ValueError(f"Return not allowed: {'; '.join(errors)}")